import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { initializeApp, deleteApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { 
  createMaintenanceContract,
  updateMaintenanceContract,
  deleteMaintenanceContract,
  markRecurringPaymentAsPaid,
  processScheduledPayments
} from '../recurring';

// Mock Firebase Admin
jest.mock('firebase-admin/app');
jest.mock('firebase-admin/firestore');

describe('Recurring Payment System', () => {
  let app: any;
  let db: any;

  beforeEach(() => {
    // Initialize test Firebase app
    app = initializeApp({
      projectId: 'test-project'
    });
    
    db = getFirestore(app);
    
    // Mock Firestore methods
    db.collection = jest.fn().mockReturnThis();
    db.doc = jest.fn().mockReturnThis();
    db.get = jest.fn();
    db.set = jest.fn();
    db.update = jest.fn();
    db.delete = jest.fn();
    db.where = jest.fn().mockReturnThis();
    db.orderBy = jest.fn().mockReturnThis();
    db.limit = jest.fn().mockReturnThis();
  });

  afterEach(async () => {
    if (app) {
      await deleteApp(app);
    }
  });

  describe('Maintenance Contract Management', () => {
    it('should create a maintenance contract successfully', async () => {
      const contractData = {
        projectId: 'test-project-1',
        clientId: 'test-client-1',
        name: 'Monthly Website Maintenance',
        description: 'Regular website maintenance and updates',
        amount: 5000,
        currency: 'INR',
        frequency: 'monthly' as const,
        startDate: '2024-01-01',
        autoGenerateInvoices: true,
        reminderDaysBefore: 3,
        gracePeriodDays: 7,
        notes: 'Test contract'
      };

      // Mock project and client existence
      db.get.mockResolvedValueOnce({ exists: true }); // project
      db.get.mockResolvedValueOnce({ exists: true }); // client

      const mockRequest = {
        auth: { uid: 'test-admin' },
        data: contractData
      };

      const result = await createMaintenanceContract(mockRequest);
      
      expect(result.success).toBe(true);
      expect(result.contractId).toBeDefined();
      expect(db.set).toHaveBeenCalled();
    });

    it('should fail to create contract with invalid project', async () => {
      const contractData = {
        projectId: 'invalid-project',
        clientId: 'test-client-1',
        name: 'Test Contract',
        amount: 5000,
        currency: 'INR',
        frequency: 'monthly' as const,
        startDate: '2024-01-01',
        autoGenerateInvoices: true,
        reminderDaysBefore: 3,
        gracePeriodDays: 7
      };

      // Mock project not found
      db.get.mockResolvedValueOnce({ exists: false });

      const mockRequest = {
        auth: { uid: 'test-admin' },
        data: contractData
      };

      await expect(createMaintenanceContract(mockRequest)).rejects.toThrow('Project not found');
    });

    it('should update maintenance contract successfully', async () => {
      const updateData = {
        contractId: 'test-contract-1',
        updates: {
          amount: 6000,
          reminderDaysBefore: 5
        }
      };

      // Mock contract exists
      db.get.mockResolvedValueOnce({ 
        exists: true,
        data: () => ({
          frequency: 'monthly',
          startDate: { toDate: () => new Date('2024-01-01') }
        })
      });

      const mockRequest = {
        auth: { uid: 'test-admin' },
        data: updateData
      };

      const result = await updateMaintenanceContract(mockRequest);
      
      expect(result.success).toBe(true);
      expect(db.update).toHaveBeenCalled();
    });

    it('should delete maintenance contract and related payments', async () => {
      const contractId = 'test-contract-1';

      // Mock contract exists
      db.get.mockResolvedValueOnce({ exists: true });
      
      // Mock related payments
      db.get.mockResolvedValueOnce({
        empty: false,
        docs: [
          { ref: { delete: jest.fn() } },
          { ref: { delete: jest.fn() } }
        ]
      });

      const mockRequest = {
        auth: { uid: 'test-admin' },
        data: { contractId }
      };

      const result = await deleteMaintenanceContract(mockRequest);
      
      expect(result.success).toBe(true);
    });
  });

  describe('Payment Processing', () => {
    it('should mark recurring payment as paid successfully', async () => {
      const paymentData = {
        paymentId: 'test-payment-1',
        supportingDocuments: [
          {
            fileName: 'receipt.pdf',
            fileData: 'base64data',
            mimeType: 'application/pdf',
            description: 'Payment receipt'
          }
        ]
      };

      // Mock payment exists
      db.get.mockResolvedValueOnce({
        exists: true,
        data: () => ({
          maintenanceContractId: 'test-contract-1',
          projectId: 'test-project-1',
          amount: 5000
        })
      });

      const mockRequest = {
        auth: { uid: 'test-admin' },
        data: paymentData
      };

      const result = await markRecurringPaymentAsPaid(mockRequest);
      
      expect(result.success).toBe(true);
      expect(db.update).toHaveBeenCalled();
    });

    it('should fail to mark non-existent payment as paid', async () => {
      const paymentData = {
        paymentId: 'non-existent-payment'
      };

      // Mock payment not found
      db.get.mockResolvedValueOnce({ exists: false });

      const mockRequest = {
        auth: { uid: 'test-admin' },
        data: paymentData
      };

      await expect(markRecurringPaymentAsPaid(mockRequest)).rejects.toThrow('Recurring payment not found');
    });
  });

  describe('Scheduled Payment Processing', () => {
    it('should process scheduled payments correctly', async () => {
      // Mock overdue payments
      const overduePayments = [
        {
          id: 'payment-1',
          data: () => ({
            maintenanceContractId: 'contract-1',
            dueDate: { toDate: () => new Date('2023-12-01') }
          })
        }
      ];

      // Mock active contracts needing new payments
      const activeContracts = [
        {
          id: 'contract-1',
          data: () => ({
            status: 'active',
            nextDueDate: { toDate: () => new Date('2024-01-01') },
            frequency: 'monthly',
            amount: 5000,
            projectId: 'project-1',
            clientId: 'client-1',
            name: 'Test Contract'
          })
        }
      ];

      // Mock Firestore queries
      db.get
        .mockResolvedValueOnce({ docs: overduePayments }) // overdue payments
        .mockResolvedValueOnce({ docs: activeContracts }) // active contracts
        .mockResolvedValueOnce({ empty: true }); // existing payments check

      const mockRequest = {
        auth: { 
          uid: 'system',
          token: { role: 'admin' }
        },
        data: { dryRun: true }
      };

      const result = await processScheduledPayments(mockRequest);
      
      expect(result.processed).toBeGreaterThan(0);
      expect(result.errors).toEqual([]);
    });
  });

  describe('Data Validation', () => {
    it('should validate contract frequency values', () => {
      const validFrequencies = ['monthly', 'quarterly', 'yearly'];
      const invalidFrequencies = ['weekly', 'daily', 'invalid'];

      validFrequencies.forEach(frequency => {
        expect(['monthly', 'quarterly', 'yearly']).toContain(frequency);
      });

      invalidFrequencies.forEach(frequency => {
        expect(['monthly', 'quarterly', 'yearly']).not.toContain(frequency);
      });
    });

    it('should validate payment status values', () => {
      const validStatuses = ['pending', 'paid', 'overdue', 'cancelled'];
      const invalidStatuses = ['processing', 'failed', 'invalid'];

      validStatuses.forEach(status => {
        expect(['pending', 'paid', 'overdue', 'cancelled']).toContain(status);
      });

      invalidStatuses.forEach(status => {
        expect(['pending', 'paid', 'overdue', 'cancelled']).not.toContain(status);
      });
    });

    it('should validate contract status values', () => {
      const validStatuses = ['active', 'paused', 'cancelled', 'expired'];
      const invalidStatuses = ['pending', 'draft', 'invalid'];

      validStatuses.forEach(status => {
        expect(['active', 'paused', 'cancelled', 'expired']).toContain(status);
      });

      invalidStatuses.forEach(status => {
        expect(['active', 'paused', 'cancelled', 'expired']).not.toContain(status);
      });
    });
  });

  describe('Date Calculations', () => {
    it('should calculate next due date correctly for monthly frequency', () => {
      const startDate = new Date('2024-01-15');
      const nextDate = new Date(startDate);
      nextDate.setMonth(nextDate.getMonth() + 1);
      
      expect(nextDate.getDate()).toBe(15);
      expect(nextDate.getMonth()).toBe(1); // February (0-indexed)
    });

    it('should calculate next due date correctly for quarterly frequency', () => {
      const startDate = new Date('2024-01-15');
      const nextDate = new Date(startDate);
      nextDate.setMonth(nextDate.getMonth() + 3);
      
      expect(nextDate.getDate()).toBe(15);
      expect(nextDate.getMonth()).toBe(3); // April (0-indexed)
    });

    it('should calculate next due date correctly for yearly frequency', () => {
      const startDate = new Date('2024-01-15');
      const nextDate = new Date(startDate);
      nextDate.setFullYear(nextDate.getFullYear() + 1);
      
      expect(nextDate.getDate()).toBe(15);
      expect(nextDate.getMonth()).toBe(0); // January (0-indexed)
      expect(nextDate.getFullYear()).toBe(2025);
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      const mockRequest = {
        auth: null,
        data: {}
      };

      await expect(createMaintenanceContract(mockRequest)).rejects.toThrow('Must be authenticated');
    });

    it('should handle database connection errors', async () => {
      db.get.mockRejectedValueOnce(new Error('Database connection failed'));

      const mockRequest = {
        auth: { uid: 'test-admin' },
        data: {
          projectId: 'test-project',
          clientId: 'test-client',
          name: 'Test Contract',
          amount: 5000,
          currency: 'INR',
          frequency: 'monthly',
          startDate: '2024-01-01',
          autoGenerateInvoices: true,
          reminderDaysBefore: 3,
          gracePeriodDays: 7
        }
      };

      await expect(createMaintenanceContract(mockRequest)).rejects.toThrow('Database connection failed');
    });
  });
});
