import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import { logger } from 'firebase-functions/v2';
import { httpsCallable } from 'firebase-admin/functions';

interface MarkPaymentAsPaidRequest {
  paymentId: string;
  supportingDocuments?: {
    fileName: string;
    fileData: string; // base64 encoded
    mimeType: string;
    description?: string;
  }[];
}

interface UploadSupportingDocumentRequest {
  recurringPaymentId: string;
  fileName: string;
  fileData: string; // base64 encoded
  mimeType: string;
  description?: string;
}

// Mark recurring payment as paid
export const markRecurringPaymentAsPaid = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as MarkPaymentAsPaidRequest;
  const db = getFirestore();

  try {
    const paymentRef = db.collection('recurringPayments').doc(data.paymentId);
    const paymentDoc = await paymentRef.get();

    if (!paymentDoc.exists) {
      throw new Error('Recurring payment not found');
    }

    const paymentData = paymentDoc.data();
    
    // Upload supporting documents if provided
    let supportingDocuments: any[] = [];
    if (data.supportingDocuments && data.supportingDocuments.length > 0) {
      supportingDocuments = await Promise.all(
        data.supportingDocuments.map(doc => 
          uploadSupportingDocumentInternal(data.paymentId, doc, request.auth!.uid)
        )
      );
    }

    // Update payment status
    const updates = {
      status: 'paid' as const,
      paidAt: FieldValue.serverTimestamp(),
      supportingDocuments: supportingDocuments,
      updatedAt: FieldValue.serverTimestamp(),
    };

    await paymentRef.update(updates);

    // Update maintenance contract statistics
    await updateContractStatistics(db, paymentData?.maintenanceContractId, paymentData?.amount);

    // Update project financial summary
    await updateProjectFinancials(db, paymentData?.projectId);

    // Create next recurring payment
    await createNextRecurringPayment(db, paymentData?.maintenanceContractId, request.auth.uid);

    logger.info(`Recurring payment marked as paid: ${data.paymentId}`);
    return { success: true };

  } catch (error) {
    logger.error('Error marking recurring payment as paid:', error);
    throw error;
  }
});

// Upload supporting document
export const uploadSupportingDocument = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as UploadSupportingDocumentRequest;
  const db = getFirestore();

  try {
    // Verify payment exists
    const paymentDoc = await db.collection('recurringPayments').doc(data.recurringPaymentId).get();
    if (!paymentDoc.exists) {
      throw new Error('Recurring payment not found');
    }

    const document = await uploadSupportingDocumentInternal(
      data.recurringPaymentId,
      {
        fileName: data.fileName,
        fileData: data.fileData,
        mimeType: data.mimeType,
        description: data.description
      },
      request.auth.uid
    );

    // Add document to payment's supporting documents array
    const paymentData = paymentDoc.data();
    const currentDocs = paymentData?.supportingDocuments || [];
    
    await db.collection('recurringPayments').doc(data.recurringPaymentId).update({
      supportingDocuments: [...currentDocs, document],
      updatedAt: FieldValue.serverTimestamp(),
    });

    logger.info(`Supporting document uploaded for payment: ${data.recurringPaymentId}`);
    return { success: true, document };

  } catch (error) {
    logger.error('Error uploading supporting document:', error);
    throw error;
  }
});

// Delete supporting document
export const deleteSupportingDocument = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { recurringPaymentId, documentId } = request.data;
  const db = getFirestore();
  const storage = getStorage();

  try {
    const paymentDoc = await db.collection('recurringPayments').doc(recurringPaymentId).get();
    if (!paymentDoc.exists) {
      throw new Error('Recurring payment not found');
    }

    const paymentData = paymentDoc.data();
    const supportingDocuments = paymentData?.supportingDocuments || [];
    
    // Find the document to delete
    const documentIndex = supportingDocuments.findIndex((doc: any) => doc.id === documentId);
    if (documentIndex === -1) {
      throw new Error('Supporting document not found');
    }

    const documentToDelete = supportingDocuments[documentIndex];

    // Delete file from storage
    try {
      const bucket = storage.bucket();
      const file = bucket.file(documentToDelete.storagePath);
      await file.delete();
    } catch (storageError) {
      logger.warn('Failed to delete file from storage:', storageError);
    }

    // Remove document from array
    supportingDocuments.splice(documentIndex, 1);

    // Update payment document
    await db.collection('recurringPayments').doc(recurringPaymentId).update({
      supportingDocuments,
      updatedAt: FieldValue.serverTimestamp(),
    });

    logger.info(`Supporting document deleted: ${documentId}`);
    return { success: true };

  } catch (error) {
    logger.error('Error deleting supporting document:', error);
    throw error;
  }
});

// Mark payment as overdue
export const markRecurringPaymentAsOverdue = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { paymentId } = request.data;
  const db = getFirestore();

  try {
    const paymentRef = db.collection('recurringPayments').doc(paymentId);
    const paymentDoc = await paymentRef.get();

    if (!paymentDoc.exists) {
      throw new Error('Recurring payment not found');
    }

    await paymentRef.update({
      status: 'overdue' as const,
      overdueAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    });

    logger.info(`Recurring payment marked as overdue: ${paymentId}`);
    return { success: true };

  } catch (error) {
    logger.error('Error marking recurring payment as overdue:', error);
    throw error;
  }
});

// Helper functions
async function uploadSupportingDocumentInternal(
  recurringPaymentId: string,
  documentData: {
    fileName: string;
    fileData: string;
    mimeType: string;
    description?: string;
  },
  uploadedBy: string
) {
  const storage = getStorage();
  const bucket = storage.bucket();
  
  // Generate unique document ID and storage path
  const documentId = generateSupportingDocumentId();
  const storagePath = `recurring-payments/${recurringPaymentId}/documents/${documentId}-${documentData.fileName}`;
  
  // Convert base64 to buffer
  const fileBuffer = Buffer.from(documentData.fileData, 'base64');
  
  // Upload to Firebase Storage
  const file = bucket.file(storagePath);
  await file.save(fileBuffer, {
    metadata: {
      contentType: documentData.mimeType,
      metadata: {
        recurringPaymentId,
        documentId,
        uploadedBy,
        originalFileName: documentData.fileName,
      }
    }
  });

  // Generate signed URL for download (valid for 1 hour)
  const [downloadUrl] = await file.getSignedUrl({
    action: 'read',
    expires: Date.now() + 60 * 60 * 1000, // 1 hour
  });

  return {
    id: documentId,
    fileName: `${documentId}-${documentData.fileName}`,
    originalFileName: documentData.fileName,
    fileSize: fileBuffer.length,
    mimeType: documentData.mimeType,
    storagePath,
    downloadUrl,
    uploadedBy,
    uploadedAt: FieldValue.serverTimestamp(),
    description: documentData.description || null,
  };
}

async function updateContractStatistics(db: any, contractId: string, paymentAmount: number) {
  const contractRef = db.collection('maintenanceContracts').doc(contractId);
  
  await contractRef.update({
    totalPaymentsMade: FieldValue.increment(1),
    totalAmountPaid: FieldValue.increment(paymentAmount),
    lastPaymentDate: FieldValue.serverTimestamp(),
    updatedAt: FieldValue.serverTimestamp(),
  });
}

async function createNextRecurringPayment(db: any, contractId: string, createdBy: string) {
  const contractDoc = await db.collection('maintenanceContracts').doc(contractId).get();
  const contract = contractDoc.data();
  
  if (!contract || contract.status !== 'active') {
    return; // Don't create next payment if contract is not active
  }

  // Calculate next due date
  const currentNextDue = contract.nextDueDate.toDate();
  const nextDueDate = new Date(currentNextDue);
  
  switch (contract.frequency) {
    case 'monthly':
      nextDueDate.setMonth(nextDueDate.getMonth() + 1);
      break;
    case 'quarterly':
      nextDueDate.setMonth(nextDueDate.getMonth() + 3);
      break;
    case 'yearly':
      nextDueDate.setFullYear(nextDueDate.getFullYear() + 1);
      break;
  }

  // Check if we've reached the end date
  if (contract.endDate && nextDueDate > contract.endDate.toDate()) {
    // Mark contract as expired
    await db.collection('maintenanceContracts').doc(contractId).update({
      status: 'expired',
      updatedAt: FieldValue.serverTimestamp(),
    });
    return;
  }

  // Update contract's next due date
  await db.collection('maintenanceContracts').doc(contractId).update({
    nextDueDate: Timestamp.fromDate(nextDueDate),
    updatedAt: FieldValue.serverTimestamp(),
  });

  // Create next payment
  const paymentId = generateRecurringPaymentId();
  
  const payment = {
    id: paymentId,
    maintenanceContractId: contractId,
    projectId: contract.projectId,
    clientId: contract.clientId,
    amount: contract.amount,
    currency: contract.currency,
    status: 'pending' as const,
    dueDate: Timestamp.fromDate(nextDueDate),
    paidAt: null,
    overdueAt: null,
    description: `${contract.frequency} maintenance payment for ${contract.name}`,
    notes: null,
    supportingDocuments: [],
    invoiceId: null,
    invoicedAt: null,
    reminderSentAt: null,
    overdueNotificationSentAt: null,
    createdBy,
    createdAt: FieldValue.serverTimestamp(),
    updatedAt: FieldValue.serverTimestamp(),
  };

  await db.collection('recurringPayments').doc(paymentId).set(payment);
}

function generateSupportingDocumentId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `ONE-DOC-${timestamp}-${random}`;
}

function generateRecurringPaymentId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `ONE-RPY-${timestamp}-${random}`;
}

async function updateProjectFinancials(db: any, projectId: string) {
  try {
    // Import and call the calculateProjectFinancials function
    const { calculateProjectFinancials } = await import('../financial/calculateProjectFinancials');
    await calculateProjectFinancials(projectId);
    logger.info(`Project financials updated for project: ${projectId}`);
  } catch (error) {
    logger.error(`Error updating project financials for ${projectId}:`, error);
  }
}
