import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

interface GetMaintenanceContractsRequest {
  filters?: {
    projectId?: string;
    clientId?: string;
    status?: string;
    frequency?: string;
    searchTerm?: string;
  };
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface GetRecurringPaymentsRequest {
  filters?: {
    maintenanceContractId?: string;
    projectId?: string;
    clientId?: string;
    status?: string;
    dueDateFrom?: string;
    dueDateTo?: string;
    searchTerm?: string;
  };
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Get maintenance contracts with filtering and pagination
export const getMaintenanceContracts = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as GetMaintenanceContractsRequest;
  const db = getFirestore();

  try {
    let query = db.collection('maintenanceContracts');
    
    // Apply filters
    if (data.filters) {
      if (data.filters.projectId) {
        query = query.where('projectId', '==', data.filters.projectId);
      }
      if (data.filters.clientId) {
        query = query.where('clientId', '==', data.filters.clientId);
      }
      if (data.filters.status) {
        query = query.where('status', '==', data.filters.status);
      }
      if (data.filters.frequency) {
        query = query.where('frequency', '==', data.filters.frequency);
      }
    }

    // Apply sorting
    const sortBy = data.sortBy || 'createdAt';
    const sortOrder = data.sortOrder || 'desc';
    query = query.orderBy(sortBy, sortOrder);

    // Apply pagination
    const limit = data.limit || 50;
    const offset = data.offset || 0;
    
    if (offset > 0) {
      const offsetQuery = db.collection('maintenanceContracts')
        .orderBy(sortBy, sortOrder)
        .limit(offset);
      const offsetSnapshot = await offsetQuery.get();
      if (!offsetSnapshot.empty) {
        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        query = query.startAfter(lastDoc);
      }
    }
    
    query = query.limit(limit);

    const snapshot = await query.get();
    
    // Get total count for pagination
    const totalQuery = db.collection('maintenanceContracts');
    const totalSnapshot = await totalQuery.get();
    const total = totalSnapshot.size;

    // Enrich contracts with project and client names
    const contracts = await Promise.all(
      snapshot.docs.map(async (doc) => {
        const contractData = doc.data();
        
        // Get project and client names
        const [projectDoc, clientDoc] = await Promise.all([
          db.collection('projects').doc(contractData.projectId).get(),
          db.collection('users').doc(contractData.clientId).get()
        ]);

        return {
          id: doc.id,
          ...contractData,
          projectName: projectDoc.exists ? projectDoc.data()?.name : 'Unknown Project',
          clientName: clientDoc.exists ? clientDoc.data()?.displayName : 'Unknown Client',
        };
      })
    );

    // Apply search filter if provided (done after enrichment for name searching)
    let filteredContracts = contracts;
    if (data.filters?.searchTerm) {
      const searchTerm = data.filters.searchTerm.toLowerCase();
      filteredContracts = contracts.filter(contract =>
        contract.name.toLowerCase().includes(searchTerm) ||
        contract.projectName.toLowerCase().includes(searchTerm) ||
        contract.clientName.toLowerCase().includes(searchTerm) ||
        (contract.description && contract.description.toLowerCase().includes(searchTerm))
      );
    }

    return {
      contracts: filteredContracts,
      total,
      limit,
      offset,
      hasMore: offset + limit < total
    };

  } catch (error) {
    logger.error('Error getting maintenance contracts:', error);
    throw error;
  }
});

// Get recurring payments with filtering and pagination
export const getRecurringPayments = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as GetRecurringPaymentsRequest;
  const db = getFirestore();

  try {
    let query = db.collection('recurringPayments');
    
    // Apply filters
    if (data.filters) {
      if (data.filters.maintenanceContractId) {
        query = query.where('maintenanceContractId', '==', data.filters.maintenanceContractId);
      }
      if (data.filters.projectId) {
        query = query.where('projectId', '==', data.filters.projectId);
      }
      if (data.filters.clientId) {
        query = query.where('clientId', '==', data.filters.clientId);
      }
      if (data.filters.status) {
        query = query.where('status', '==', data.filters.status);
      }
      if (data.filters.dueDateFrom) {
        const fromDate = Timestamp.fromDate(new Date(data.filters.dueDateFrom));
        query = query.where('dueDate', '>=', fromDate);
      }
      if (data.filters.dueDateTo) {
        const toDate = Timestamp.fromDate(new Date(data.filters.dueDateTo));
        query = query.where('dueDate', '<=', toDate);
      }
    }

    // Apply sorting
    const sortBy = data.sortBy || 'dueDate';
    const sortOrder = data.sortOrder || 'desc';
    query = query.orderBy(sortBy, sortOrder);

    // Apply pagination
    const limit = data.limit || 50;
    const offset = data.offset || 0;
    
    if (offset > 0) {
      const offsetQuery = db.collection('recurringPayments')
        .orderBy(sortBy, sortOrder)
        .limit(offset);
      const offsetSnapshot = await offsetQuery.get();
      if (!offsetSnapshot.empty) {
        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        query = query.startAfter(lastDoc);
      }
    }
    
    query = query.limit(limit);

    const snapshot = await query.get();
    
    // Get total count for pagination
    const totalQuery = db.collection('recurringPayments');
    const totalSnapshot = await totalQuery.get();
    const total = totalSnapshot.size;

    // Enrich payments with related data
    const payments = await Promise.all(
      snapshot.docs.map(async (doc) => {
        const paymentData = doc.data();
        
        // Get contract, project, and client names
        const [contractDoc, projectDoc, clientDoc] = await Promise.all([
          db.collection('maintenanceContracts').doc(paymentData.maintenanceContractId).get(),
          db.collection('projects').doc(paymentData.projectId).get(),
          db.collection('users').doc(paymentData.clientId).get()
        ]);

        return {
          id: doc.id,
          ...paymentData,
          contractName: contractDoc.exists ? contractDoc.data()?.name : 'Unknown Contract',
          projectName: projectDoc.exists ? projectDoc.data()?.name : 'Unknown Project',
          clientName: clientDoc.exists ? clientDoc.data()?.displayName : 'Unknown Client',
          supportingDocumentsCount: paymentData.supportingDocuments?.length || 0,
        };
      })
    );

    // Apply search filter if provided
    let filteredPayments = payments;
    if (data.filters?.searchTerm) {
      const searchTerm = data.filters.searchTerm.toLowerCase();
      filteredPayments = payments.filter(payment =>
        payment.contractName.toLowerCase().includes(searchTerm) ||
        payment.projectName.toLowerCase().includes(searchTerm) ||
        payment.clientName.toLowerCase().includes(searchTerm) ||
        (payment.description && payment.description.toLowerCase().includes(searchTerm))
      );
    }

    return {
      payments: filteredPayments,
      total,
      limit,
      offset,
      hasMore: offset + limit < total
    };

  } catch (error) {
    logger.error('Error getting recurring payments:', error);
    throw error;
  }
});

// Get recurring payment dashboard summary
export const getRecurringPaymentSummary = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const db = getFirestore();
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);

  try {
    // Get all active contracts
    const activeContractsSnapshot = await db
      .collection('maintenanceContracts')
      .where('status', '==', 'active')
      .get();

    // Calculate total monthly revenue from active contracts
    let totalMonthlyRevenue = 0;
    activeContractsSnapshot.docs.forEach(doc => {
      const contract = doc.data();
      if (contract.frequency === 'monthly') {
        totalMonthlyRevenue += contract.amount;
      } else if (contract.frequency === 'quarterly') {
        totalMonthlyRevenue += contract.amount / 3;
      } else if (contract.frequency === 'yearly') {
        totalMonthlyRevenue += contract.amount / 12;
      }
    });

    // Get upcoming payments (next 30 days)
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    
    const upcomingPaymentsSnapshot = await db
      .collection('recurringPayments')
      .where('status', '==', 'pending')
      .where('dueDate', '<=', Timestamp.fromDate(thirtyDaysFromNow))
      .get();

    // Get overdue payments
    const overduePaymentsSnapshot = await db
      .collection('recurringPayments')
      .where('status', '==', 'overdue')
      .get();

    // Get payments made this month
    const paymentsThisMonthSnapshot = await db
      .collection('recurringPayments')
      .where('status', '==', 'paid')
      .where('paidAt', '>=', Timestamp.fromDate(startOfMonth))
      .where('paidAt', '<=', Timestamp.fromDate(endOfMonth))
      .get();

    // Calculate revenue this month
    let revenueThisMonth = 0;
    paymentsThisMonthSnapshot.docs.forEach(doc => {
      const payment = doc.data();
      revenueThisMonth += payment.amount;
    });

    // Get contracts expiring this month
    const contractsExpiringSnapshot = await db
      .collection('maintenanceContracts')
      .where('status', '==', 'active')
      .where('endDate', '>=', Timestamp.fromDate(startOfMonth))
      .where('endDate', '<=', Timestamp.fromDate(endOfMonth))
      .get();

    return {
      totalActiveContracts: activeContractsSnapshot.size,
      totalMonthlyRevenue,
      upcomingPayments: upcomingPaymentsSnapshot.size,
      overduePayments: overduePaymentsSnapshot.size,
      paymentsThisMonth: paymentsThisMonthSnapshot.size,
      revenueThisMonth,
      contractsExpiringThisMonth: contractsExpiringSnapshot.size,
    };

  } catch (error) {
    logger.error('Error getting recurring payment summary:', error);
    throw error;
  }
});

// Get upcoming payments
export const getUpcomingPayments = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { days = 30 } = request.data;
  const db = getFirestore();
  const now = new Date();
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);

  try {
    const snapshot = await db
      .collection('recurringPayments')
      .where('status', '==', 'pending')
      .where('dueDate', '>=', Timestamp.fromDate(now))
      .where('dueDate', '<=', Timestamp.fromDate(futureDate))
      .orderBy('dueDate', 'asc')
      .get();

    const payments = await Promise.all(
      snapshot.docs.map(async (doc) => {
        const paymentData = doc.data();
        
        // Get related data
        const [contractDoc, projectDoc, clientDoc] = await Promise.all([
          db.collection('maintenanceContracts').doc(paymentData.maintenanceContractId).get(),
          db.collection('projects').doc(paymentData.projectId).get(),
          db.collection('users').doc(paymentData.clientId).get()
        ]);

        const dueDate = paymentData.dueDate.toDate();
        const daysUntilDue = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

        return {
          id: doc.id,
          contractName: contractDoc.exists ? contractDoc.data()?.name : 'Unknown Contract',
          projectName: projectDoc.exists ? projectDoc.data()?.name : 'Unknown Project',
          clientName: clientDoc.exists ? clientDoc.data()?.displayName : 'Unknown Client',
          amount: paymentData.amount,
          currency: paymentData.currency,
          dueDate: paymentData.dueDate,
          daysUntilDue,
          status: paymentData.status,
        };
      })
    );

    return { payments };

  } catch (error) {
    logger.error('Error getting upcoming payments:', error);
    throw error;
  }
});

// Get overdue payments
export const getOverduePayments = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const db = getFirestore();
  const now = new Date();

  try {
    const snapshot = await db
      .collection('recurringPayments')
      .where('status', '==', 'overdue')
      .orderBy('dueDate', 'asc')
      .get();

    const payments = await Promise.all(
      snapshot.docs.map(async (doc) => {
        const paymentData = doc.data();
        
        // Get related data
        const [contractDoc, projectDoc, clientDoc] = await Promise.all([
          db.collection('maintenanceContracts').doc(paymentData.maintenanceContractId).get(),
          db.collection('projects').doc(paymentData.projectId).get(),
          db.collection('users').doc(paymentData.clientId).get()
        ]);

        const dueDate = paymentData.dueDate.toDate();
        const daysOverdue = Math.ceil((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

        return {
          id: doc.id,
          contractName: contractDoc.exists ? contractDoc.data()?.name : 'Unknown Contract',
          projectName: projectDoc.exists ? projectDoc.data()?.name : 'Unknown Project',
          clientName: clientDoc.exists ? clientDoc.data()?.displayName : 'Unknown Client',
          amount: paymentData.amount,
          currency: paymentData.currency,
          dueDate: paymentData.dueDate,
          daysOverdue,
          overdueAt: paymentData.overdueAt,
          status: paymentData.status,
        };
      })
    );

    return { payments };

  } catch (error) {
    logger.error('Error getting overdue payments:', error);
    throw error;
  }
});
